"use client";

import { useState } from "react";
import { Plus, FileText, Users, Clock, TrendingUp } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useInvoiceFileStats } from "./hooks/useInvoiceFileStats";
import { ViewInvoiceFiles } from "./components/ViewInvoiceFiles";
import { AddInvoiceFile } from "./components/AddInvoiceFile";
import { InvoiceFilesProvider } from "./invoiceFilesContext";
import { PermissionWrapper } from "@/lib/permissionWrapper";

interface InvoiceFilesBoardProps {
  userData: any;
  carrier: any;
  users: any;
  permissions: any;
}
export default function InvoiceFilesBoard({
  userData,
  carrier,
  users,
  permissions,
}: InvoiceFilesBoardProps) {
  const [showAddDialog, setShowAddDialog] = useState(false);
  const { data: stats, isLoading: statsLoading } = useInvoiceFileStats();

  const metricCards = [
    {
      title: "Total Files",
      value: stats?.totalFiles || 0,
      description: "All invoice files",
      icon: FileText,
      trend: "+12% from last month",
    },
    {
      title: "Pending Assignment",
      value: stats?.pendingAssignment || 0,
      description: "Files awaiting assignment",
      icon: Clock,
      trend: "-5% from last week",
    },
    {
      title: "Active Users",
      value: stats?.activeUsers || 0,
      description: "Users with assignments",
      icon: Users,
      trend: "+2 new this week",
    },
    {
      title: "Pages Processed",
      value: stats?.totalPages || 0,
      description: "Total pages this month",
      icon: TrendingUp,
      trend: "+18% from last month",
    },
  ];

  return (
    <InvoiceFilesProvider>
      <div className="flex-1 space-y-6 p-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Invoice Files</h1>
            <p className="text-muted-foreground">
              Manage and track invoice file processing and assignments
            </p>
          </div>
          <div className="flex items-center space-x-2">
            {/* <Button variant="outline">Export</Button> */}
            <PermissionWrapper
              permissions={permissions}
              requiredPermissions={["create-invoice-files"]}
            >
            <Button onClick={() => setShowAddDialog(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add New File
            </Button>
            </PermissionWrapper>
          </div>
        </div>

        {/* Metrics Cards */}
        {/* <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {metricCards.map((card, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {card.title}
              </CardTitle>
              <card.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {statsLoading ? "..." : card.value.toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">
                {card.description}
              </p>
              <p className="text-xs text-green-600 mt-1">{card.trend}</p>
            </CardContent>
          </Card>
        ))}
      </div> */}

        {/* Main Content */}
        <Tabs defaultValue="files" className="space-y-4">
          <TabsList>
            <TabsTrigger value="files">All Files</TabsTrigger>
            {/* <TabsTrigger value="recent">Recent Activity</TabsTrigger> */}
          </TabsList>

          <TabsContent value="files" className="space-y-4">
            <PermissionWrapper
              permissions={permissions}
              requiredPermissions={["view-invoice-files"]}
            >
              <ViewInvoiceFiles
                userData={userData}
                carrier={carrier}
                users={users}
                permissions={permissions}
              />
            </PermissionWrapper>
          </TabsContent>

          <TabsContent value="recent" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>
                  Latest updates and changes to invoice files
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  Recent activity feed will be implemented here
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Add Invoice File Dialog */}
        <PermissionWrapper
          permissions={permissions}
          requiredPermissions={["create-invoice-files"]}
        >
        <AddInvoiceFile
          open={showAddDialog}
          onOpenChange={setShowAddDialog}
          userData={userData}
          users={users}
          carriers={carrier}
        />
        </PermissionWrapper>
      </div>
    </InvoiceFilesProvider>
  );
}
