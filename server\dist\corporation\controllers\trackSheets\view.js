"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getInvoiceStats = exports.getRecievedDatesByInvoice = exports.ViewTrackSheetsById = exports.viewTrackSheets = void 0;
const helpers_1 = require("../../../utils/helpers");
const prismaClient_1 = __importDefault(require("../../../utils/prismaClient"));
const viewTrackSheets = async (req, res) => {
    try {
        const clientId = req.params.id;
        if (!clientId) {
            return res.status(400).json({ error: "clientId is required" });
        }
        const { recievedFDate, recievedTDate, invoiceFDate, invoiceTDate, shipmentFDate, shipmentTDate, Client, Company, Division, Carrier, ["FTP File Name"]: ftpFileName, ["FTP Page"]: ftp_Page, ["Master Invoice"]: MasterInvoice, Invoice, Bol, ["Invoice Total"]: InvoiceTotal, Currency, ["Qty Shipped"]: QtyShipped, ["Quantity Billed"]: QuantityBilledText, ["Invoice Status"]: InvoiceStatus, ["Manual Matching"]: ManualMatching, ["Invoice Type"]: InvoiceType, ["Weight Unit Name"]: WeightUnitName, ["Savings"]: Savings, ["Doc Available"]: DocAvailable, Notes, Mistake, ["Entered by"]: EnteredBy, ["ORCA STATUS"]: manifestStatus, page, pageSize, systemGeneratedWarnings, ...customFieldParams } = req.query;
        const take = Number(pageSize);
        const skip = (Number(page) - 1) * Number(pageSize);
        const whereClause = { AND: [] };
        const searchConditions = [];
        if (systemGeneratedWarnings === "true") {
            searchConditions.push({
                systemGeneratedWarnings: {
                    some: {}
                }
            });
        }
        else if (systemGeneratedWarnings === "false") {
            searchConditions.push({
                systemGeneratedWarnings: {
                    none: {}
                }
            });
        }
        if (Client) {
            const clientList = Client.split(",").map((item) => item.trim());
            searchConditions.push({
                OR: clientList.map((clientName) => ({
                    client: {
                        client_name: {
                            contains: clientName,
                            mode: "insensitive",
                        },
                    },
                })),
            });
        }
        if (Company) {
            const companyList = Company.split(",").map((item) => item.trim());
            searchConditions.push({
                OR: companyList.map((company) => ({
                    company: {
                        contains: company,
                        mode: "insensitive",
                    },
                })),
            });
        }
        if (Division) {
            const divisionList = Division.split(",").map((item) => item.trim());
            searchConditions.push({
                OR: divisionList.map((division) => ({
                    division: {
                        contains: division,
                        mode: "insensitive",
                    },
                })),
            });
        }
        if (Carrier) {
            const carrierList = Carrier.split(",").map((item) => item.trim());
            searchConditions.push({
                OR: carrierList.map((carrier) => ({
                    carrier: {
                        name: {
                            contains: carrier,
                            mode: "insensitive",
                        },
                    },
                })),
            });
        }
        if (ftpFileName) {
            const ftpFileNameList = ftpFileName.split(",").map((item) => item.trim());
            searchConditions.push({
                OR: ftpFileNameList.map((ftpFileName) => ({
                    ftpFileName: {
                        contains: ftpFileName,
                        mode: "insensitive",
                    },
                })),
            });
        }
        if (ftp_Page) {
            const ftpPageList = ftp_Page.split(",").map((item) => item.trim());
            searchConditions.push({
                OR: ftpPageList.map((ftpPages) => ({
                    ftpPage: {
                        contains: ftpPages,
                        mode: "insensitive",
                    },
                })),
            });
        }
        if (MasterInvoice) {
            const masterInvoiceList = MasterInvoice.split(",").map((item) => item.trim());
            searchConditions.push({
                OR: masterInvoiceList.map((masterInvoice) => ({
                    masterInvoice: {
                        contains: masterInvoice,
                        mode: "insensitive",
                    },
                })),
            });
        }
        if (Invoice) {
            const invoiceList = Invoice.split(",").map((item) => item.trim());
            searchConditions.push({
                OR: invoiceList.map((invoice) => ({
                    invoice: {
                        contains: invoice,
                        mode: "insensitive",
                    },
                })),
            });
        }
        if (Bol) {
            const bolList = Bol.split(",").map((item) => item.trim());
            searchConditions.push({
                OR: bolList.map((bol) => ({
                    bol: {
                        contains: bol,
                        mode: "insensitive",
                    },
                })),
            });
        }
        if (invoiceFDate || invoiceTDate) {
            const dateRangeFilter = {};
            if (invoiceFDate)
                dateRangeFilter.gte = new Date(invoiceFDate);
            if (invoiceTDate)
                dateRangeFilter.lte = new Date(invoiceTDate);
            searchConditions.push({
                invoiceDate: dateRangeFilter,
            });
        }
        if (recievedFDate || recievedTDate) {
            const dateRangeFilter = {};
            if (recievedFDate)
                dateRangeFilter.gte = new Date(recievedFDate);
            if (recievedTDate)
                dateRangeFilter.lte = new Date(recievedTDate);
            searchConditions.push({
                receivedDate: dateRangeFilter,
            });
        }
        if (shipmentFDate || shipmentTDate) {
            const dateRangeFilter = {};
            if (shipmentFDate)
                dateRangeFilter.gte = new Date(shipmentFDate);
            if (shipmentTDate)
                dateRangeFilter.lte = new Date(shipmentTDate);
            searchConditions.push({
                shipmentDate: dateRangeFilter,
            });
        }
        if (InvoiceTotal) {
            const invoiceTotalList = InvoiceTotal.split(",").map((item) => item.trim());
            searchConditions.push({
                OR: invoiceTotalList.map((invoiceTotal) => ({
                    invoiceTotal: {
                        contains: invoiceTotal,
                        mode: "insensitive",
                    },
                })),
            });
        }
        if (Currency) {
            const currencyList = Currency.split(",").map((item) => item.trim());
            searchConditions.push({
                OR: currencyList.map((currency) => ({
                    currency: {
                        contains: currency,
                        mode: "insensitive",
                    },
                })),
            });
        }
        if (QtyShipped) {
            const qtyShippedList = QtyShipped.split(",").map((item) => item.trim());
            searchConditions.push({
                OR: qtyShippedList.map((qtyShipped) => ({
                    qtyShipped: {
                        contains: qtyShipped,
                        mode: "insensitive",
                    },
                })),
            });
        }
        if (QuantityBilledText) {
            const quantityBilledTextList = QuantityBilledText.split(",").map((item) => item.trim());
            searchConditions.push({
                OR: quantityBilledTextList.map((quantityBilledText) => ({
                    quantityBilledText: {
                        contains: quantityBilledText,
                        mode: "insensitive",
                    },
                })),
            });
        }
        if (InvoiceStatus) {
            const invoiceStatusList = InvoiceStatus.split(",").map((item) => item.trim());
            searchConditions.push({
                OR: invoiceStatusList.map((invoiceStatus) => ({
                    invoiceStatus: {
                        contains: invoiceStatus,
                        mode: "insensitive",
                    },
                })),
            });
        }
        if (ManualMatching) {
            const manualMatchingList = ManualMatching.split(",").map((item) => item.trim());
            searchConditions.push({
                OR: manualMatchingList.map((manualMatching) => ({
                    manualMatching: {
                        contains: manualMatching,
                        mode: "insensitive",
                    },
                })),
            });
        }
        if (InvoiceType) {
            const invoiceTypeList = InvoiceType.split(",").map((item) => item.trim());
            searchConditions.push({
                OR: invoiceTypeList.map((invoiceType) => ({
                    invoiceType: {
                        contains: invoiceType,
                        mode: "insensitive",
                    },
                })),
            });
        }
        if (WeightUnitName) {
            const weightUnitNameList = WeightUnitName.split(",").map((item) => item.trim());
            searchConditions.push({
                OR: weightUnitNameList.map((weightUnitName) => ({
                    weightUnitName: {
                        contains: weightUnitName,
                        mode: "insensitive",
                    },
                })),
            });
        }
        if (Savings) {
            const savingsList = Savings.split(",").map((item) => item.trim());
            searchConditions.push({
                OR: savingsList.map((savings) => ({
                    savings: {
                        contains: savings,
                        mode: "insensitive",
                    },
                })),
            });
        }
        if (DocAvailable) {
            const docAvailableList = DocAvailable.split(",").map((item) => item.trim());
            searchConditions.push({
                OR: docAvailableList.map((docAvailable) => ({
                    docAvailable: {
                        contains: docAvailable,
                        mode: "insensitive",
                    },
                })),
            });
        }
        if (Notes) {
            const notesList = Notes.split(",").map((item) => item.trim());
            searchConditions.push({
                OR: notesList.map((notes) => ({
                    notes: {
                        contains: notes,
                        mode: "insensitive",
                    },
                })),
            });
        }
        if (Mistake) {
            const mistakeList = Mistake.split(",").map((item) => item.trim());
            searchConditions.push({
                OR: mistakeList.map((mistake) => ({
                    mistake: {
                        contains: mistake,
                        mode: "insensitive",
                    },
                })),
            });
        }
        if (EnteredBy) {
            const enteredByList = EnteredBy.split(",").map((item) => item.trim());
            searchConditions.push({
                OR: enteredByList.map((enteredBy) => ({
                    enteredBy: {
                        contains: enteredBy,
                        mode: "insensitive",
                    },
                })),
            });
        }
        // Add search for manifestDetails.orcaStatus
        if (manifestStatus) {
            const orcaStatusList = manifestStatus.split(",").map((item) => item.trim());
            searchConditions.push({
                manifestDetails: {
                    OR: orcaStatusList.map((status) => ({
                        manifestStatus: {
                            contains: status,
                            mode: "insensitive",
                        },
                    })),
                },
            });
        }
        Object.keys(customFieldParams).forEach((key) => {
            if (key.startsWith("customField_") && (key.endsWith('_from') || key.endsWith('_to'))) {
                // Date range search for custom fields
                const match = key.match(/^customField_(.+)_(from|to)$/);
                if (match) {
                    const fieldId = match[1];
                    const bound = match[2];
                    const fromKey = `customField_${fieldId}_from`;
                    const toKey = `customField_${fieldId}_to`;
                    const fromValue = customFieldParams[fromKey];
                    const toValue = customFieldParams[toKey];
                    if (fromValue || toValue) {
                        const valueFilter = {};
                        if (fromValue)
                            valueFilter.gte = fromValue;
                        if (toValue)
                            valueFilter.lte = toValue;
                        searchConditions.push({
                            TrackSheetCustomFieldMapping: {
                                some: {
                                    customField: { id: fieldId },
                                    value: valueFilter,
                                },
                            },
                        });
                    }
                }
            }
            else if (key.startsWith("customField_") && !key.endsWith('_from') && !key.endsWith('_to')) {
                // Existing logic for non-date custom fields
                const fieldId = key.replace("customField_", "");
                const searchValue = customFieldParams[key];
                if (searchValue && typeof searchValue === "string") {
                    const valueList = searchValue.split(",").map((item) => item.trim());
                    searchConditions.push({
                        TrackSheetCustomFieldMapping: {
                            some: {
                                customField: { id: fieldId },
                                OR: valueList.map((value) => ({
                                    value: { contains: value, mode: "insensitive" },
                                })),
                            },
                        },
                    });
                }
            }
        });
        if (searchConditions.length > 0) {
            whereClause.AND = searchConditions;
        }
        // Build where clause for trackSheets, including all search filters
        const trackSheetWhere = {
            clientId: Number(clientId),
            ...(searchConditions.length > 0 ? { AND: searchConditions } : {}),
        };
        const data = await prismaClient_1.default.trackSheets.findMany({
            where: trackSheetWhere,
            take: page ? take : undefined,
            skip: page ? skip : undefined,
            select: {
                id: true,
                clientId: true,
                company: true,
                division: true,
                masterInvoice: true,
                invoice: true,
                bol: true,
                invoiceDate: true,
                receivedDate: true,
                shipmentDate: true,
                invoiceStatus: true,
                manualMatching: true,
                invoiceType: true,
                currency: true,
                qtyShipped: true,
                weightUnitName: true,
                quantityBilledText: true,
                freightClass: true,
                invoiceTotal: true,
                savings: true,
                financialNotes: true,
                ftpFileName: true,
                ftpPage: true,
                filePath: true,
                billToClient: true,
                docAvailable: true,
                notes: true,
                mistake: true,
                shipperAddress: true,
                consigneeAddress: true,
                billToAddress: true,
                freightTerm: true,
                shipperAddressType: true,
                consigneeAddressType: true,
                billToAddressType: true,
                finalInvoice: true,
                manifestDetails: {
                    select: {
                        manifestStatus: true,
                        manifestDate: true,
                        manifestNotes: true,
                        actionRequired: true
                    }
                },
                enteredBy: true,
                createdAt: true,
                client: {
                    select: {
                        id: true,
                        client_name: true,
                    },
                },
                carrier: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
                systemGeneratedWarnings: true,
                TrackSheetCustomFieldMapping: {
                    select: {
                        id: true,
                        customFieldId: true,
                        value: true,
                    },
                },
            },
            orderBy: { id: "desc" },
        });
        // Fetch all tickets for the returned tracksheet IDs to check existence efficiently
        const trackSheetIds = data.map(item => item.id);
        const tickets = await prismaClient_1.default.ticket.findMany({
            where: {
                workItemId: { in: trackSheetIds }
            },
            select: { workItemId: true, id: true }
        });
        const ticketMap = new Map(tickets.map(t => [t.workItemId, t.id]));
        // Transform the data to ensure decimal places are preserved and add ticketId if exists
        const transformedData = data.map(item => ({
            ...item,
            invoiceTotal: item.invoiceTotal ? Number(item.invoiceTotal).toFixed(2) : null,
            ticketId: ticketMap.get(item.id) || null
        }));
        const datalength = await prismaClient_1.default.trackSheets.count({
            where: trackSheetWhere,
        });
        if (transformedData.length > 0) {
            return res.status(200).json({ data: transformedData, datalength });
        }
        return res.status(400).json([]);
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.viewTrackSheets = viewTrackSheets;
const ViewTrackSheetsById = async (req, res) => {
    const { id } = req.params;
    try {
        const data = await prismaClient_1.default.trackSheets.findUnique({
            where: { id: Number(id) },
            include: {
                client: true,
                carrier: true,
                systemGeneratedWarnings: true,
            },
        });
        if (data) {
            return res.status(200).json(data);
        }
        return res.status(404).json({ message: "TrackSheet not found" });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.ViewTrackSheetsById = ViewTrackSheetsById;
const getRecievedDatesByInvoice = async (req, res) => {
    try {
        const { invoice } = req.query;
        if (!invoice || invoice.length < 3) {
            return res.status(400).json({ error: "Invoice search requires at least 3 characters" });
        }
        const data = await prismaClient_1.default.trackSheets.findMany({
            where: {
                invoice: {
                    equals: invoice,
                    mode: 'insensitive'
                }
            },
            select: {
                receivedDate: true,
                invoice: true
            },
            orderBy: {
                receivedDate: 'desc'
            },
        });
        if (data.length > 0) {
            return res.status(200).json(data);
        }
        return res.status(404).json({ message: "No matching invoices found" });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.getRecievedDatesByInvoice = getRecievedDatesByInvoice;
const getInvoiceStats = async (req, res) => {
    //console.log('getInvoiceStats called with clientId:', req.query.clientId);
    try {
        const clientId = req.query.clientId ? Number(req.query.clientId) : undefined;
        if (!clientId) {
            return res.status(400).json({ error: 'clientId is required' });
        }
        const where = { clientId };
        // Total invoices for this client
        const totalInvoices = await prismaClient_1.default.trackSheets.count({ where });
        // Today's invoices for this client
        const startOfToday = new Date();
        startOfToday.setHours(0, 0, 0, 0);
        const endOfToday = new Date();
        endOfToday.setHours(23, 59, 59, 999);
        const invoicesToday = await prismaClient_1.default.trackSheets.count({
            where: {
                ...where,
                createdAt: {
                    gte: startOfToday,
                    lte: endOfToday,
                },
            },
        });
        return res.json({
            totalInvoices,
            invoicesToday,
        });
    }
    catch (error) {
        (0, helpers_1.handleError)(res, error);
    }
};
exports.getInvoiceStats = getInvoiceStats;
//# sourceMappingURL=view.js.map